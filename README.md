# 停车场管理系统

一个基于Python Flask + MySQL的停车场管理系统，提供用户管理、车位管理、车辆进出管理和账单查询等功能。

## 功能特性

### 1. 用户登录和权限管理
- 用户登录验证
- 会话管理
- 权限控制

### 2. 用户信息管理
- 用户信息的增删改查
- 用户账户余额管理
- 用户车牌号管理

### 3. 车位信息管理
- 车位信息的增删改查
- 车位状态管理（空闲中/已占用）
- 车位收费标准设置

### 4. 车辆进出管理
- 车辆驶入登记
- 车辆驶出处理
- 停车记录查询

### 5. 账单管理
- 自动计费功能
- 账单记录查询
- 收入统计

## 技术栈

- **后端**: Python Flask
- **数据库**: MySQL
- **前端**: HTML + CSS + JavaScript + jQuery
- **UI框架**: 自定义CSS样式

## 数据库设计

### 用户表 (users)
- id: 主键
- username: 用户名
- password_hash: 密码哈希
- name: 姓名
- gender: 性别
- phone: 联系电话
- car_number: 车牌号
- balance: 账户余额
- created_at: 创建时间

### 车位信息表 (parking_spaces)
- id: 主键
- space_number: 车位号 (如A01)
- area: 车位区域 (如阳光小区)
- status: 车位状态 (空闲中/已占用)
- hourly_rate: 车位费 (元/小时)
- created_at: 创建时间

### 泊车信息表 (parking_records)
- id: 主键
- car_number: 车牌号
- space_number: 车位号
- entry_time: 驶入时间
- exit_time: 驶出时间 (可为空)
- hourly_rate: 车位费
- created_at: 创建时间

### 账单表 (bills)
- id: 主键
- car_number: 车牌号
- parking_hours: 停车时间 (小时，不满一小时向上取整)
- total_fee: 停车总费用
- created_at: 创建时间

## 安装和运行

### 1. 环境要求
- Python 3.7+
- MySQL 5.7+

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 数据库配置
1. 创建MySQL数据库:
```sql
CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `.env` 文件中的数据库配置:
```
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=parking_system
```

### 4. 启动系统
```bash
python start.py
```

或者直接运行:
```bash
python app.py
```

### 5. 访问系统
打开浏览器访问: http://localhost:5000

## 默认账号

- **管理员账号**: admin / admin123
- **普通用户账号**: user / user123

## 业务流程

### 车辆驶入流程
1. 在"车辆进出管理"页面点击"车辆驶入"
2. 输入车牌号，选择空闲车位
3. 系统记录驶入时间，更新车位状态为"已占用"

### 车辆驶出流程
1. 在"车辆进出管理"页面找到对应记录
2. 点击"驶出打票"按钮
3. 系统自动计算停车费用（不满一小时向上取整）
4. 生成账单记录，更新车位状态为"空闲中"

### 计费规则
- 按小时计费，不满一小时按一小时计算
- 费用 = 停车小时数 × 车位单价

## 项目结构

```
parking/
├── app.py              # 主应用文件
├── init_db.py          # 数据库初始化脚本
├── start.py            # 启动脚本
├── requirements.txt    # 依赖包列表
├── .env               # 环境配置文件
├── README.md          # 项目说明文档
├── static/            # 静态文件目录
│   ├── css/
│   │   └── style.css  # 样式文件
│   └── js/
│       └── main.js    # JavaScript文件
└── templates/         # 模板文件目录
    ├── base.html      # 基础模板
    ├── login.html     # 登录页面
    ├── index.html     # 首页
    ├── users.html     # 用户管理页面
    ├── parking_spaces.html  # 车位管理页面
    ├── parking_records.html # 车辆进出管理页面
    └── bills.html     # 账单查询页面
```

## 注意事项

1. 确保MySQL服务已启动
2. 首次运行前需要创建数据库
3. 修改数据库配置信息
4. 系统默认运行在5000端口

## 开发者信息

本系统基于Flask框架开发，采用前后端分离的设计模式，提供RESTful API接口。
