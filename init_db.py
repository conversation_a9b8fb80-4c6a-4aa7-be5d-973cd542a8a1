from app import app, db, User, ParkingSpace, ParkingRecord, <PERSON>
from werkzeug.security import generate_password_hash

def init_database():
    """初始化数据库"""
    with app.app_context():
        # 删除所有表
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        # 创建默认管理员用户
        admin = User(
            username='admin',
            password_hash=generate_password_hash('admin123')
        )
        db.session.add(admin)

        # 创建普通用户
        user = User(
            username='user',
            password_hash=generate_password_hash('user123')
        )
        db.session.add(user)
        
        # 创建示例车位数据
        parking_spaces = [
            ParkingSpace(space_number='A01', area='阳光小区', status='空闲中', hourly_rate=10.00),
            ParkingSpace(space_number='A02', area='阳光小区', status='空闲中', hourly_rate=10.00),
            ParkingSpace(space_number='A03', area='阳光小区', status='空闲中', hourly_rate=10.00),
            ParkingSpace(space_number='A04', area='阳光小区', status='空闲中', hourly_rate=10.00),
            ParkingSpace(space_number='B01', area='阳光小区', status='空闲中', hourly_rate=3.00),
            ParkingSpace(space_number='B02', area='阳光小区', status='空闲中', hourly_rate=3.00),
        ]
        
        for space in parking_spaces:
            db.session.add(space)
        
        # 提交所有更改
        db.session.commit()
        
        print("数据库初始化完成！")
        print("管理员账号: admin / admin123")
        print("普通用户账号: user / user123")

if __name__ == '__main__':
    init_database()
