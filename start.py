#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场管理系统启动脚本
"""

import os
import sys
from app import app, db
from init_db import init_database

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import flask_sqlalchemy
        import pymysql
        import werkzeug
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        with app.app_context():
            db.engine.execute('SELECT 1')
        print("✓ MySQL连接正常")
        return True
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("请确保MySQL服务已启动，并检查数据库配置")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("停车场管理系统启动检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查MySQL连接
    if not check_mysql_connection():
        print("\n如果是首次运行，请先创建数据库:")
        print("CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        sys.exit(1)
    
    # 初始化数据库
    try:
        init_database()
        print("✓ 数据库初始化完成")
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("系统启动成功！")
    print("=" * 50)
    print("访问地址: http://localhost:5000")
    print("管理员账号: admin / admin123")
    print("普通用户账号: user / user123")
    print("=" * 50)
    
    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    main()
