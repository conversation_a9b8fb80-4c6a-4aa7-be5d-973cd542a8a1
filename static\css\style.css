/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 登录页面样式 */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><style>.cls-1{fill:%23ff6b6b;}.cls-2{fill:%234ecdc4;}.cls-3{fill:%2345b7d1;}.cls-4{fill:%2396ceb4;}.cls-5{fill:%23feca57;}</style></defs><circle class="cls-1" cx="200" cy="200" r="50"/><circle class="cls-2" cx="400" cy="150" r="30"/><circle class="cls-3" cx="600" cy="250" r="40"/><circle class="cls-4" cx="800" cy="180" r="35"/><circle class="cls-5" cx="1000" cy="220" r="45"/></svg>');
    opacity: 0.1;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 400px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.login-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 30px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #666;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-right: 10px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
}

/* 主界面样式 */
.main-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: #2c3e50;
    color: white;
    padding: 0;
}

.sidebar-header {
    padding: 20px;
    background: #34495e;
    border-bottom: 1px solid #3a4a5c;
}

.sidebar-title {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    border-bottom: 1px solid #3a4a5c;
}

.sidebar-menu a {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    transition: background-color 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background-color: #3498db;
}

.sidebar-menu i {
    margin-right: 10px;
    width: 16px;
}

.content {
    flex: 1;
    background: #ecf0f1;
}

.header {
    background: white;
    padding: 15px 30px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
}

.header-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.main-content {
    padding: 30px;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

.btn-info {
    background-color: #3498db;
    color: white;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    gap: 10px;
}

.pagination span {
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .content {
        order: 1;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
    }
    
    .search-form {
        flex-direction: column;
        width: 100%;
    }
    
    .search-input {
        width: 100%;
    }
}
