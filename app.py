from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta, timezone
import os
from dotenv import load_dotenv
import math

# 加载环境变量
load_dotenv()

app = Flask(__name__)
# app.config['SECRET_KEY'] = 'simple-key'  # 简单的密钥用于session
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/parking_system'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# 数据库模型
class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    car_number = db.Column(db.String(20))
    balance = db.Column(db.Numeric(10, 2), default=0.00)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class ParkingSpace(db.Model):
    __tablename__ = 'parking_spaces'
    id = db.Column(db.Integer, primary_key=True)
    space_number = db.Column(db.String(10), unique=True, nullable=False)  # 车位号 eg.A01
    area = db.Column(db.String(50), nullable=False)  # 车位区域 eg.阳光小区
    status = db.Column(db.String(20), default='空闲中')  # 车位状态
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=False)  # 车位费 eg.10 rmb/h
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class ParkingRecord(db.Model):
    __tablename__ = 'parking_records'
    id = db.Column(db.Integer, primary_key=True)
    car_number = db.Column(db.String(20), nullable=False)  # 车牌号
    space_number = db.Column(db.String(10), nullable=False)  # 车位号
    entry_time = db.Column(db.DateTime, nullable=False)  # 驶入时间
    exit_time = db.Column(db.DateTime)  # 驶出时间（可以为空）
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=False)  # 该车位的车位费
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class Bill(db.Model):
    __tablename__ = 'bills'
    id = db.Column(db.Integer, primary_key=True)
    car_number = db.Column(db.String(20), nullable=False)  # 车牌号
    parking_hours = db.Column(db.Integer, nullable=False)  # 停车时间（小时，不满一小时向上取整）
    total_fee = db.Column(db.Numeric(10, 2), nullable=False)  # 停车总费用
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

# 辅助函数
def login_required(f):
    """登录验证装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def calculate_parking_fee(entry_time, exit_time, hourly_rate):
    """计算停车费用"""
    if not exit_time:
        return 0

    duration = exit_time - entry_time
    hours = math.ceil(duration.total_seconds() / 3600)  # 不满一小时向上取整
    return float(hourly_rate) * hours

# 路由定义
@app.route('/')
def index():
    """首页"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['name'] = user.name
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='用户名或密码错误')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    return redirect(url_for('login'))

@app.route('/users')
@login_required
def users():
    """用户信息管理页面"""
    return render_template('users.html')

@app.route('/parking_spaces')
@login_required
def parking_spaces():
    """车位信息管理页面"""
    return render_template('parking_spaces.html')

@app.route('/parking_records')
@login_required
def parking_records():
    """泊车信息管理页面"""
    return render_template('parking_records.html')

@app.route('/bills')
@login_required
def bills():
    """账单查询页面"""
    return render_template('bills.html')

# API路由
@app.route('/api/users', methods=['GET'])
@login_required
def api_get_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    users = User.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'users': [{
            'id': user.id,
            'username': user.username,
            'name': user.name,
            'gender': user.gender,
            'phone': user.phone,
            'car_number': user.car_number,
            'balance': float(user.balance)
        } for user in users.items],
        'total': users.total,
        'pages': users.pages,
        'current_page': page
    })

@app.route('/api/users', methods=['POST'])
@login_required
def api_create_user():
    """创建用户"""
    data = request.get_json()

    # 检查用户名是否已存在
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400

    user = User(
        username=data['username'],
        password_hash=generate_password_hash(data['password']),
        name=data['name'],
        gender=data['gender'],
        phone=data['phone'],
        car_number=data.get('car_number', ''),
        balance=data.get('balance', 0.00)
    )

    db.session.add(user)
    db.session.commit()

    return jsonify({'message': '用户创建成功'}), 201

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
def api_update_user(user_id):
    """更新用户信息"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()

    user.name = data.get('name', user.name)
    user.gender = data.get('gender', user.gender)
    user.phone = data.get('phone', user.phone)
    user.car_number = data.get('car_number', user.car_number)
    user.balance = data.get('balance', user.balance)

    db.session.commit()

    return jsonify({'message': '用户信息更新成功'})

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def api_delete_user(user_id):
    """删除用户"""
    user = User.query.get_or_404(user_id)
    db.session.delete(user)
    db.session.commit()

    return jsonify({'message': '用户删除成功'})

# 车位管理API
@app.route('/api/parking_spaces', methods=['GET'])
@login_required
def api_get_parking_spaces():
    """获取车位列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    spaces = ParkingSpace.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'spaces': [{
            'id': space.id,
            'space_number': space.space_number,
            'area': space.area,
            'status': space.status,
            'hourly_rate': float(space.hourly_rate)
        } for space in spaces.items],
        'total': spaces.total,
        'pages': spaces.pages,
        'current_page': page
    })

@app.route('/api/parking_spaces', methods=['POST'])
@login_required
def api_create_parking_space():
    """创建车位"""
    data = request.get_json()

    # 检查车位号是否已存在
    if ParkingSpace.query.filter_by(space_number=data['space_number']).first():
        return jsonify({'error': '车位号已存在'}), 400

    space = ParkingSpace(
        space_number=data['space_number'],
        area=data['area'],
        status=data.get('status', '空闲中'),
        hourly_rate=data['hourly_rate']
    )

    db.session.add(space)
    db.session.commit()

    return jsonify({'message': '车位创建成功'}), 201

@app.route('/api/parking_spaces/<int:space_id>', methods=['PUT'])
@login_required
def api_update_parking_space(space_id):
    """更新车位信息"""
    space = ParkingSpace.query.get_or_404(space_id)
    data = request.get_json()

    space.space_number = data.get('space_number', space.space_number)
    space.area = data.get('area', space.area)
    space.status = data.get('status', space.status)
    space.hourly_rate = data.get('hourly_rate', space.hourly_rate)

    db.session.commit()

    return jsonify({'message': '车位信息更新成功'})

# 泊车记录API
@app.route('/api/parking_records', methods=['GET'])
@login_required
def api_get_parking_records():
    """获取泊车记录列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    records = ParkingRecord.query.order_by(ParkingRecord.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'records': [{
            'id': record.id,
            'car_number': record.car_number,
            'space_number': record.space_number,
            'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
            'exit_time': record.exit_time.strftime('%Y-%m-%d %H:%M:%S') if record.exit_time else None,
            'hourly_rate': float(record.hourly_rate),
            'area': ParkingSpace.query.filter_by(space_number=record.space_number).first().area if ParkingSpace.query.filter_by(space_number=record.space_number).first() else '未知区域'
        } for record in records.items],
        'total': records.total,
        'pages': records.pages,
        'current_page': page
    })

@app.route('/api/parking_records', methods=['POST'])
@login_required
def api_create_parking_record():
    """车辆驶入"""
    data = request.get_json()

    # 检查车位是否存在且空闲
    space = ParkingSpace.query.filter_by(space_number=data['space_number']).first()
    if not space:
        return jsonify({'error': '车位不存在'}), 400

    if space.status != '空闲中':
        return jsonify({'error': '车位已被占用'}), 400

    # 检查车辆是否已在停车场内
    existing_record = ParkingRecord.query.filter_by(
        car_number=data['car_number'],
        exit_time=None
    ).first()

    if existing_record:
        return jsonify({'error': '该车辆已在停车场内'}), 400

    # 创建泊车记录
    record = ParkingRecord(
        car_number=data['car_number'],
        space_number=data['space_number'],
        entry_time=datetime.now(),
        hourly_rate=space.hourly_rate
    )

    # 更新车位状态
    space.status = '已占用'

    db.session.add(record)
    db.session.commit()

    return jsonify({'message': '车辆驶入成功'}), 201

@app.route('/api/parking_records/<int:record_id>/exit', methods=['PUT'])
@login_required
def api_exit_parking(record_id):
    """车辆驶出"""
    record = ParkingRecord.query.get_or_404(record_id)

    if record.exit_time:
        return jsonify({'error': '车辆已驶出'}), 400

    # 设置驶出时间
    exit_time = datetime.now()
    record.exit_time = exit_time

    # 计算费用并创建账单
    parking_hours = math.ceil((exit_time - record.entry_time).total_seconds() / 3600)
    total_fee = parking_hours * float(record.hourly_rate)

    bill = Bill(
        car_number=record.car_number,
        parking_hours=parking_hours,
        total_fee=total_fee
    )

    # 更新车位状态
    space = ParkingSpace.query.filter_by(space_number=record.space_number).first()
    if space:
        space.status = '空闲中'

    db.session.add(bill)
    db.session.commit()

    return jsonify({
        'message': '车辆驶出成功',
        'parking_hours': parking_hours,
        'total_fee': total_fee
    })

# 账单管理API
@app.route('/api/bills', methods=['GET'])
@login_required
def api_get_bills():
    """获取账单列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 10

    bills = Bill.query.order_by(Bill.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'bills': [{
            'id': bill.id,
            'car_number': bill.car_number,
            'parking_hours': bill.parking_hours,
            'total_fee': float(bill.total_fee),
            'created_at': bill.created_at.strftime('%Y-%m-%d %H:%M:%S')
        } for bill in bills.items],
        'total': bills.total,
        'pages': bills.pages,
        'current_page': page
    })

@app.route('/api/parking_spaces/<int:space_id>', methods=['DELETE'])
@login_required
def api_delete_parking_space(space_id):
    """删除车位"""
    space = ParkingSpace.query.get_or_404(space_id)
    db.session.delete(space)
    db.session.commit()

    return jsonify({'message': '车位删除成功'})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # 创建默认管理员用户
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                name='系统管理员',
                gender='男',
                phone='13574659211'
            )
            db.session.add(admin)
            db.session.commit()

    app.run(debug=True, host='0.0.0.0', port=5000)
