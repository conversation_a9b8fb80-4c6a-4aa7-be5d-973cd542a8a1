#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置向导
"""

import pymysql
import os

def test_mysql_connection(host, user, password):
    """测试MySQL连接"""
    try:
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        connection.close()
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False

def create_database(host, user, password, database):
    """创建数据库"""
    try:
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.close()
        connection.close()
        return True
    except Exception as e:
        print(f"创建数据库失败: {e}")
        return False

def update_env_file(host, user, password, database):
    """更新.env文件"""
    env_content = f"""MYSQL_HOST={host}
MYSQL_USER={user}
MYSQL_PASSWORD={password}
MYSQL_DATABASE={database}
SECRET_KEY=your-secret-key-here
"""
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)

def main():
    print("=" * 60)
    print("停车场管理系统配置向导")
    print("=" * 60)
    
    print("\n请输入MySQL数据库配置信息:")
    
    # 获取配置信息
    host = input("MySQL主机地址 (默认: localhost): ").strip() or "localhost"
    user = input("MySQL用户名 (默认: root): ").strip() or "root"
    password = input("MySQL密码: ").strip()
    database = input("数据库名称 (默认: parking_system): ").strip() or "parking_system"
    
    print(f"\n正在测试连接到 {user}@{host}...")
    
    # 测试连接
    if not test_mysql_connection(host, user, password):
        print("✗ MySQL连接失败，请检查配置信息")
        return
    
    print("✓ MySQL连接成功")
    
    # 创建数据库
    print(f"正在创建数据库 {database}...")
    if not create_database(host, user, password, database):
        print("✗ 数据库创建失败")
        return
    
    print("✓ 数据库创建成功")
    
    # 更新配置文件
    print("正在更新配置文件...")
    update_env_file(host, user, password, database)
    print("✓ 配置文件更新成功")
    
    print("\n" + "=" * 60)
    print("配置完成！")
    print("下一步:")
    print("1. 运行: python init_db.py  (初始化数据库)")
    print("2. 运行: python run.py      (启动系统)")
    print("=" * 60)

if __name__ == '__main__':
    main()
