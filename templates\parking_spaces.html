{% extends "base.html" %}

{% block page_title %}车位信息管理{% endblock %}

{% block content %}
<div class="table-container">
    <div class="table-header">
        <div class="search-form">
            <input type="text" placeholder="请输入车位号" class="search-input" id="spaceNumberInput">
            <input type="text" placeholder="请输入车位区域" class="search-input" id="areaInput">
            <input type="text" placeholder="请输入车位状态" class="search-input" id="statusInput">
            <button class="btn btn-info btn-sm" onclick="searchSpaces()">搜索</button>
            <button class="btn btn-success btn-sm" onclick="showAddSpaceModal()">新增</button>
        </div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>序号</th>
                <th>车位号</th>
                <th>车位区域</th>
                <th>车位类型</th>
                <th>车位状态</th>
                <th>车位收费</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="spaceTableBody">
            <!-- 数据将通过JavaScript动态加载 -->
        </tbody>
    </table>
    
    <div class="pagination" id="pagination">
        <!-- 分页将通过JavaScript动态生成 -->
    </div>
</div>

<!-- 添加车位模态框 -->
<div id="addSpaceModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 400px;">
        <h3 style="margin-bottom: 20px;">添加车位</h3>
        <form id="addSpaceForm">
            <div class="form-group">
                <label>车位号</label>
                <input type="text" name="space_number" class="form-control" required>
            </div>
            <div class="form-group">
                <label>车位区域</label>
                <input type="text" name="area" class="form-control" required>
            </div>
            <div class="form-group">
                <label>车位状态</label>
                <select name="status" class="form-control" required>
                    <option value="空闲中">空闲中</option>
                    <option value="已占用">已占用</option>
                </select>
            </div>
            <div class="form-group">
                <label>车位收费（元/小时）</label>
                <input type="number" name="hourly_rate" class="form-control" required step="0.01">
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideAddSpaceModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑车位模态框 -->
<div id="editSpaceModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 400px;">
        <h3 style="margin-bottom: 20px;">编辑车位</h3>
        <form id="editSpaceForm">
            <input type="hidden" name="space_id" id="editSpaceId">
            <div class="form-group">
                <label>车位号</label>
                <input type="text" name="space_number" class="form-control" id="editSpaceNumber" required>
            </div>
            <div class="form-group">
                <label>车位区域</label>
                <input type="text" name="area" class="form-control" id="editArea" required>
            </div>
            <div class="form-group">
                <label>车位状态</label>
                <select name="status" class="form-control" id="editStatus" required>
                    <option value="空闲中">空闲中</option>
                    <option value="已占用">已占用</option>
                </select>
            </div>
            <div class="form-group">
                <label>车位收费（元/小时）</label>
                <input type="number" name="hourly_rate" class="form-control" id="editHourlyRate" required step="0.01">
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideEditSpaceModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;

// 页面加载时获取车位列表
$(document).ready(function() {
    loadSpaces();
});

// 加载车位列表
function loadSpaces(page = 1) {
    currentPage = page;
    $.get('/api/parking_spaces?page=' + page, function(data) {
        displaySpaces(data.spaces);
        displayPagination(data.current_page, data.pages, data.total);
    });
}

// 显示车位列表
function displaySpaces(spaces) {
    const tbody = $('#spaceTableBody');
    tbody.empty();
    
    spaces.forEach((space, index) => {
        const spaceType = space.hourly_rate >= 10 ? '标准车位' : '临时车位';
        const row = `
            <tr>
                <td>${(currentPage - 1) * 10 + index + 1}</td>
                <td>${space.space_number}</td>
                <td>${space.area}</td>
                <td>${spaceType}</td>
                <td>${space.status}</td>
                <td>${space.hourly_rate}元/时</td>
                <td>
                    <button class="btn btn-info btn-sm" onclick="editSpace(${space.id})">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteSpace(${space.id})">删除</button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 显示分页
function displayPagination(currentPage, totalPages, totalItems) {
    const pagination = $('#pagination');
    pagination.html(`
        <span>共 ${totalItems} 条，共 ${totalPages} 页</span>
        <button class="btn btn-sm" onclick="loadSpaces(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>
        <span>第 ${currentPage} 页</span>
        <button class="btn btn-sm" onclick="loadSpaces(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>
    `);
}

// 显示添加车位模态框
function showAddSpaceModal() {
    $('#addSpaceModal').show();
}

// 隐藏添加车位模态框
function hideAddSpaceModal() {
    $('#addSpaceModal').hide();
    const form = $('#addSpaceForm')[0];
    form.reset();
    // 重置按钮状态
    $(form).find('button[type="submit"]').prop('disabled', false).text('保存');
}

// 显示编辑车位模态框
function showEditSpaceModal() {
    $('#editSpaceModal').show();
}

// 隐藏编辑车位模态框
function hideEditSpaceModal() {
    $('#editSpaceModal').hide();
    $('#editSpaceForm')[0].reset();
}

// 添加车位表单提交
$('#addSpaceForm').submit(function(e) {
    e.preventDefault();

    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('提交中...');

    const formData = {
        space_number: $('input[name="space_number"]').val(),
        area: $('input[name="area"]').val(),
        status: $('select[name="status"]').val(),
        hourly_rate: parseFloat($('input[name="hourly_rate"]').val())
    };

    $.ajax({
        url: '/api/parking_spaces',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('车位添加成功');
            hideAddSpaceModal();
            loadSpaces(currentPage);
        },
        error: function(xhr) {
            const error = JSON.parse(xhr.responseText);
            alert('添加失败：' + error.error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

// 编辑车位
function editSpace(spaceId) {
    // 这里应该先获取车位详情，然后填充到编辑表单中
    // 为了简化，这里直接显示编辑模态框
    showEditSpaceModal();
    $('#editSpaceId').val(spaceId);
}

// 编辑车位表单提交
$('#editSpaceForm').submit(function(e) {
    e.preventDefault();
    
    const spaceId = $('#editSpaceId').val();
    const formData = {
        space_number: $('#editSpaceNumber').val(),
        area: $('#editArea').val(),
        status: $('#editStatus').val(),
        hourly_rate: parseFloat($('#editHourlyRate').val())
    };
    
    $.ajax({
        url: '/api/parking_spaces/' + spaceId,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('车位更新成功');
            hideEditSpaceModal();
            loadSpaces(currentPage);
        },
        error: function(xhr) {
            alert('更新失败');
        }
    });
});

// 删除车位
function deleteSpace(spaceId) {
    if (confirm('确定要删除这个车位吗？')) {
        $.ajax({
            url: '/api/parking_spaces/' + spaceId,
            method: 'DELETE',
            success: function(response) {
                alert('车位删除成功');
                loadSpaces(currentPage);
            },
            error: function(xhr) {
                alert('删除失败');
            }
        });
    }
}

// 搜索车位
function searchSpaces() {
    const spaceNumber = $('#spaceNumberInput').val().trim();
    const area = $('#areaInput').val().trim();
    const status = $('#statusInput').val().trim();

    let searchParams = [];
    if (spaceNumber) searchParams.push('space_number=' + encodeURIComponent(spaceNumber));
    if (area) searchParams.push('area=' + encodeURIComponent(area));
    if (status) searchParams.push('status=' + encodeURIComponent(status));

    if (searchParams.length > 0) {
        $.get('/api/parking_spaces?' + searchParams.join('&'), function(data) {
            displaySpaces(data.spaces);
            displayPagination(1, 1, data.spaces.length);
        }).fail(function() {
            alert('搜索失败，请重试');
        });
    } else {
        loadSpaces(1);
    }
}
</script>
{% endblock %}
