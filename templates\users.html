{% extends "base.html" %}

{% block page_title %}用户信息管理{% endblock %}

{% block content %}
<div class="table-container">
    <div class="table-header">
        <div class="search-form">
            <input type="text" placeholder="请输入用户名" class="search-input" id="searchInput">
            <button class="btn btn-info btn-sm" onclick="searchUsers()">搜索</button>
            <button class="btn btn-success btn-sm" onclick="showAddUserModal()">新增</button>
        </div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>序号</th>
                <th>用户名</th>
                <th>密码</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="userTableBody">
            <!-- 数据将通过JavaScript动态加载 -->
        </tbody>
    </table>
    
    <div class="pagination" id="pagination">
        <!-- 分页将通过JavaScript动态生成 -->
    </div>
</div>

<!-- 添加用户模态框 -->
<div id="addUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 400px;">
        <h3 style="margin-bottom: 20px;">添加用户</h3>
        <form id="addUserForm">
            <div class="form-group">
                <label>用户名</label>
                <input type="text" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" name="password" class="form-control" required>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideAddUserModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div id="editUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 400px;">
        <h3 style="margin-bottom: 20px;">编辑用户</h3>
        <form id="editUserForm">
            <input type="hidden" id="editUserId">
            <div class="form-group">
                <label>用户名</label>
                <input type="text" id="editUsername" class="form-control" readonly>
            </div>
            <div class="form-group">
                <label>新密码（留空则不修改）</label>
                <input type="password" id="editPassword" class="form-control">
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideEditUserModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;

// 页面加载时获取用户列表
$(document).ready(function() {
    loadUsers();
});

// 加载用户列表
function loadUsers(page = 1) {
    currentPage = page;
    $.get('/api/users?page=' + page, function(data) {
        displayUsers(data.users);
        displayPagination(data.current_page, data.pages, data.total);
    });
}

// 显示用户列表
function displayUsers(users) {
    const tbody = $('#userTableBody');
    tbody.empty();
    
    users.forEach((user, index) => {
        const row = `
            <tr>
                <td>${(currentPage - 1) * 10 + index + 1}</td>
                <td>${user.username}</td>
                <td>
                    <span id="password-${user.id}" style="font-family: monospace;">******</span>
                    <button class="btn btn-sm" onclick="togglePassword(${user.id})" style="border: none; background: none; color: #666; margin-left: 5px;">
                        <i id="eye-${user.id}" class="fas fa-eye"></i>
                    </button>
                </td>
                <td>
                    <button class="btn btn-info btn-sm" onclick="editUser(${user.id})">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id})">删除</button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 显示分页
function displayPagination(currentPage, totalPages, totalItems) {
    const pagination = $('#pagination');
    pagination.html(`
        <span>共 ${totalItems} 条，共 ${totalPages} 页</span>
        <button class="btn btn-sm" onclick="loadUsers(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>
        <span>第 ${currentPage} 页</span>
        <button class="btn btn-sm" onclick="loadUsers(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>
    `);
}

// 显示添加用户模态框
function showAddUserModal() {
    $('#addUserModal').show();
}

// 隐藏添加用户模态框
function hideAddUserModal() {
    $('#addUserModal').hide();
    const form = $('#addUserForm')[0];
    form.reset();
    // 重置按钮状态
    $(form).find('button[type="submit"]').prop('disabled', false).text('保存');
}

// 添加用户表单提交
$('#addUserForm').submit(function(e) {
    e.preventDefault();

    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('提交中...');

    const formData = {
        username: $('input[name="username"]').val(),
        password: $('input[name="password"]').val()
    };

    $.ajax({
        url: '/api/users',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('用户添加成功');
            hideAddUserModal();
            loadUsers(currentPage);
        },
        error: function(xhr) {
            const error = JSON.parse(xhr.responseText);
            alert('添加失败：' + error.error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

// 删除用户
function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？')) {
        $.ajax({
            url: '/api/users/' + userId,
            method: 'DELETE',
            success: function(response) {
                alert('用户删除成功');
                loadUsers(currentPage);
            },
            error: function(xhr) {
                alert('删除失败');
            }
        });
    }
}

// 存储用户密码显示状态
let passwordVisible = {};

// 切换密码显示/隐藏
function togglePassword(userId) {
    const passwordSpan = $(`#password-${userId}`);
    const eyeIcon = $(`#eye-${userId}`);

    if (!passwordVisible[userId]) {
        // 显示密码提示
        passwordSpan.text('[已加密，无法查看原文]');
        eyeIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        passwordVisible[userId] = true;
    } else {
        // 隐藏密码
        passwordSpan.text('******');
        eyeIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        passwordVisible[userId] = false;
    }
}

// 显示编辑用户模态框
function showEditUserModal() {
    $('#editUserModal').show();
}

// 隐藏编辑用户模态框
function hideEditUserModal() {
    $('#editUserModal').hide();
    const form = $('#editUserForm')[0];
    form.reset();
    // 重置按钮状态
    $(form).find('button[type="submit"]').prop('disabled', false).text('保存');
}

// 编辑用户
function editUser(userId) {
    // 获取用户信息
    $.get('/api/users/' + userId, function(user) {
        $('#editUserId').val(user.id);
        $('#editUsername').val(user.username);
        $('#editPassword').val('');
        showEditUserModal();
    }).fail(function() {
        alert('获取用户信息失败');
    });
}

// 编辑用户表单提交
$('#editUserForm').submit(function(e) {
    e.preventDefault();

    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('提交中...');

    const userId = $('#editUserId').val();
    const password = $('#editPassword').val();

    const formData = {};
    if (password.trim()) {
        formData.password = password;
    }

    if (Object.keys(formData).length === 0) {
        alert('请输入要修改的信息');
        submitBtn.prop('disabled', false).text(originalText);
        return;
    }

    $.ajax({
        url: '/api/users/' + userId,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('用户信息更新成功');
            hideEditUserModal();
            loadUsers(currentPage);
        },
        error: function(xhr) {
            alert('更新失败');
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

// 搜索用户
function searchUsers() {
    const searchTerm = $('#searchInput').val().trim();
    if (searchTerm) {
        // 如果有搜索词，使用搜索API
        $.get('/api/users?search=' + encodeURIComponent(searchTerm), function(data) {
            displayUsers(data.users);
            displayPagination(1, 1, data.users.length);
        }).fail(function() {
            alert('搜索失败，请重试');
        });
    } else {
        // 如果没有搜索词，加载所有用户
        loadUsers(1);
    }
}
</script>
{% endblock %}
