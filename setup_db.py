#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库设置脚本
"""

import pymysql
from app import app, db
from init_db import init_database

def create_database_if_not_exists():
    """如果数据库不存在则创建"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',  # 请根据实际情况修改密码
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✓ 数据库创建成功或已存在")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库创建失败: {e}")
        print("请检查MySQL服务是否启动，用户名密码是否正确")
        return False

def main():
    print("=" * 50)
    print("停车场管理系统数据库设置")
    print("=" * 50)
    
    # 创建数据库
    if not create_database_if_not_exists():
        print("数据库创建失败，请手动创建数据库:")
        print("CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        return
    
    # 初始化数据库表和数据
    try:
        print("正在初始化数据库表和数据...")
        init_database()
        print("✓ 数据库初始化完成")
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        print("请检查数据库连接配置")
        return
    
    print("\n" + "=" * 50)
    print("数据库设置完成！")
    print("现在可以启动系统了: python run.py")
    print("=" * 50)

if __name__ == '__main__':
    main()
