// 全局JavaScript函数

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 显示加载状态
function showLoading() {
    // 可以添加加载动画
    console.log('Loading...');
}

// 隐藏加载状态
function hideLoading() {
    console.log('Loading complete');
}

// 通用的AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('AJAX Error:', thrownError);
    if (xhr.status === 401) {
        alert('登录已过期，请重新登录');
        window.location.href = '/login';
    }
});

// 通用的确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 通用的成功提示
function showSuccess(message) {
    alert(message);
}

// 通用的错误提示
function showError(message) {
    alert('错误：' + message);
}

// 重置表单按钮状态
function resetFormButton(form) {
    const submitBtn = $(form).find('button[type="submit"]');
    submitBtn.prop('disabled', false).text('保存');
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 设置AJAX默认配置
    $.ajaxSetup({
        beforeSend: showLoading,
        complete: hideLoading
    });
});
