#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场管理系统测试脚本
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def test_login():
    """测试登录功能"""
    print("测试登录功能...")
    
    # 测试登录页面
    response = requests.get(f'{BASE_URL}/login')
    if response.status_code == 200:
        print("✓ 登录页面访问正常")
    else:
        print("✗ 登录页面访问失败")
        return False
    
    # 测试登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    session = requests.Session()
    response = session.post(f'{BASE_URL}/login', data=login_data)
    
    if response.status_code == 200 and 'login' not in response.url:
        print("✓ 管理员登录成功")
        return session
    else:
        print("✗ 管理员登录失败")
        return False

def test_api_endpoints(session):
    """测试API接口"""
    print("\n测试API接口...")
    
    # 测试用户API
    response = session.get(f'{BASE_URL}/api/users')
    if response.status_code == 200:
        print("✓ 用户API正常")
    else:
        print("✗ 用户API异常")
    
    # 测试车位API
    response = session.get(f'{BASE_URL}/api/parking_spaces')
    if response.status_code == 200:
        print("✓ 车位API正常")
    else:
        print("✗ 车位API异常")
    
    # 测试泊车记录API
    response = session.get(f'{BASE_URL}/api/parking_records')
    if response.status_code == 200:
        print("✓ 泊车记录API正常")
    else:
        print("✗ 泊车记录API异常")
    
    # 测试账单API
    response = session.get(f'{BASE_URL}/api/bills')
    if response.status_code == 200:
        print("✓ 账单API正常")
    else:
        print("✗ 账单API异常")

def test_pages(session):
    """测试页面访问"""
    print("\n测试页面访问...")
    
    pages = [
        ('/', '首页'),
        ('/users', '用户管理'),
        ('/parking_spaces', '车位管理'),
        ('/parking_records', '泊车记录'),
        ('/bills', '账单查询')
    ]
    
    for url, name in pages:
        response = session.get(f'{BASE_URL}{url}')
        if response.status_code == 200:
            print(f"✓ {name}页面正常")
        else:
            print(f"✗ {name}页面异常")

def test_business_logic(session):
    """测试业务逻辑"""
    print("\n测试业务逻辑...")
    
    # 测试添加车位
    space_data = {
        'space_number': 'TEST01',
        'area': '测试区域',
        'status': '空闲中',
        'hourly_rate': 5.0
    }
    
    response = session.post(
        f'{BASE_URL}/api/parking_spaces',
        json=space_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 201:
        print("✓ 车位添加功能正常")
    else:
        print("✗ 车位添加功能异常")
    
    # 测试车辆驶入
    entry_data = {
        'car_number': '测试A001',
        'space_number': 'TEST01'
    }
    
    response = session.post(
        f'{BASE_URL}/api/parking_records',
        json=entry_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 201:
        print("✓ 车辆驶入功能正常")
        
        # 获取记录ID用于测试驶出
        records_response = session.get(f'{BASE_URL}/api/parking_records')
        if records_response.status_code == 200:
            records = records_response.json()['records']
            test_record = None
            for record in records:
                if record['car_number'] == '测试A001':
                    test_record = record
                    break
            
            if test_record:
                # 等待一秒钟再测试驶出
                time.sleep(1)
                
                # 测试车辆驶出
                response = session.put(f'{BASE_URL}/api/parking_records/{test_record["id"]}/exit')
                if response.status_code == 200:
                    print("✓ 车辆驶出功能正常")
                    print("✓ 自动计费功能正常")
                else:
                    print("✗ 车辆驶出功能异常")
    else:
        print("✗ 车辆驶入功能异常")

def main():
    """主测试函数"""
    print("=" * 50)
    print("停车场管理系统功能测试")
    print("=" * 50)
    
    try:
        # 测试登录
        session = test_login()
        if not session:
            print("\n测试失败：无法登录系统")
            return
        
        # 测试API接口
        test_api_endpoints(session)
        
        # 测试页面访问
        test_pages(session)
        
        # 测试业务逻辑
        test_business_logic(session)
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("=" * 50)
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保系统已启动")
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    print("aaaaaaas")
    main()
