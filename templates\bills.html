{% extends "base.html" %}

{% block page_title %}登录日志查询{% endblock %}

{% block content %}
<div class="table-container">
    <div class="table-header">
        <div class="search-form">
            <input type="text" placeholder="请输入车牌号" class="search-input" id="carNumberInput">
            <input type="date" class="search-input" id="startDateInput">
            <span>至</span>
            <input type="date" class="search-input" id="endDateInput">
            <button class="btn btn-info btn-sm" onclick="searchBills()">搜索</button>
        </div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>序号</th>
                <th>车牌号</th>
                <th>停车时长</th>
                <th>停车费用</th>
                <th>生成时间</th>
            </tr>
        </thead>
        <tbody id="billTableBody">
            <!-- 数据将通过JavaScript动态加载 -->
        </tbody>
    </table>
    
    <div class="pagination" id="pagination">
        <!-- 分页将通过JavaScript动态生成 -->
    </div>
</div>

<!-- 统计信息卡片 -->
<div style="margin-top: 30px; display: flex; gap: 20px; flex-wrap: wrap;">
    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; min-width: 200px;">
        <h4 style="color: #2c3e50; margin-bottom: 10px;">
            <i class="fas fa-file-invoice" style="color: #3498db;"></i> 总账单数
        </h4>
        <p style="font-size: 24px; font-weight: bold; color: #e74c3c; margin: 0;" id="totalBills">0</p>
    </div>
    
    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; min-width: 200px;">
        <h4 style="color: #2c3e50; margin-bottom: 10px;">
            <i class="fas fa-money-bill-wave" style="color: #27ae60;"></i> 总收入
        </h4>
        <p style="font-size: 24px; font-weight: bold; color: #27ae60; margin: 0;" id="totalRevenue">0.00元</p>
    </div>
    
    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; min-width: 200px;">
        <h4 style="color: #2c3e50; margin-bottom: 10px;">
            <i class="fas fa-clock" style="color: #f39c12;"></i> 总停车时长
        </h4>
        <p style="font-size: 24px; font-weight: bold; color: #f39c12; margin: 0;" id="totalHours">0小时</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let allBills = [];

// 页面加载时获取账单列表
$(document).ready(function() {
    loadBills();
});

// 加载账单列表
function loadBills(page = 1) {
    currentPage = page;
    $.get('/api/bills?page=' + page, function(data) {
        allBills = data.bills;
        displayBills(data.bills);
        displayPagination(data.current_page, data.pages, data.total);
        updateStatistics(data.bills);
    });
}

// 显示账单列表
function displayBills(bills) {
    const tbody = $('#billTableBody');
    tbody.empty();
    
    bills.forEach((bill, index) => {
        const row = `
            <tr>
                <td>${(currentPage - 1) * 10 + index + 1}</td>
                <td>${bill.car_number}</td>
                <td>${bill.parking_hours}小时</td>
                <td>${bill.total_fee}元</td>
                <td>${bill.created_at}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 显示分页
function displayPagination(currentPage, totalPages, totalItems) {
    const pagination = $('#pagination');
    pagination.html(`
        <span>共 ${totalItems} 条，共 ${totalPages} 页</span>
        <button class="btn btn-sm" onclick="loadBills(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>
        <span>第 ${currentPage} 页</span>
        <button class="btn btn-sm" onclick="loadBills(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>
    `);
}

// 更新统计信息
function updateStatistics(bills) {
    const totalBills = bills.length;
    const totalRevenue = bills.reduce((sum, bill) => sum + parseFloat(bill.total_fee), 0);
    const totalHours = bills.reduce((sum, bill) => sum + bill.parking_hours, 0);
    
    $('#totalBills').text(totalBills);
    $('#totalRevenue').text(totalRevenue.toFixed(2) + '元');
    $('#totalHours').text(totalHours + '小时');
}

// 搜索账单
function searchBills() {
    const carNumber = $('#carNumberInput').val().trim();
    const startDate = $('#startDateInput').val();
    const endDate = $('#endDateInput').val();

    let searchParams = [];
    if (carNumber) searchParams.push('car_number=' + encodeURIComponent(carNumber));
    if (startDate) searchParams.push('start_date=' + encodeURIComponent(startDate));
    if (endDate) searchParams.push('end_date=' + encodeURIComponent(endDate));

    if (searchParams.length > 0) {
        $.get('/api/bills?' + searchParams.join('&'), function(data) {
            displayBills(data.bills);
            displayPagination(1, 1, data.bills.length);
            updateStatistics(data.bills);
        }).fail(function() {
            alert('搜索失败，请重试');
        });
    } else {
        loadBills(1);
    }
}

// 设置默认日期范围（最近30天）
$(document).ready(function() {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#endDateInput').val(today.toISOString().split('T')[0]);
    $('#startDateInput').val(thirtyDaysAgo.toISOString().split('T')[0]);
});
</script>
{% endblock %}
