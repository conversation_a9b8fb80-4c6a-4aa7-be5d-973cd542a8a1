#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("开始测试...")

try:
    print("1. 测试Flask导入...")
    from flask import Flask
    print("✓ Flask导入成功")
    
    print("2. 测试SQLAlchemy导入...")
    from flask_sqlalchemy import SQLAlchemy
    print("✓ SQLAlchemy导入成功")
    
    print("3. 测试PyMySQL导入...")
    import pymysql
    print("✓ PyMySQL导入成功")
    
    print("4. 测试app模块导入...")
    from app import app, db
    print("✓ app模块导入成功")
    
    print("5. 测试数据库模型...")
    from app import User, ParkingSpace, ParkingRecord, Bill
    print("✓ 数据库模型导入成功")
    
    print("\n所有测试通过！系统可以正常启动。")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
