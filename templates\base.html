<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}停车场管理系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="main-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">
                    <i class="fas fa-parking"></i> 停车场管理系统
                </h3>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{{ url_for('index') }}" class="{% if request.endpoint == 'index' %}active{% endif %}">
                        <i class="fas fa-home"></i> 首页
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('users') }}" class="{% if request.endpoint == 'users' %}active{% endif %}">
                        <i class="fas fa-users"></i> 用户信息管理
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('parking_spaces') }}" class="{% if request.endpoint == 'parking_spaces' %}active{% endif %}">
                        <i class="fas fa-car"></i> 车位信息管理
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('parking_records') }}" class="{% if request.endpoint == 'parking_records' %}active{% endif %}">
                        <i class="fas fa-clipboard-list"></i> 车辆进出管理
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('bills') }}" class="{% if request.endpoint == 'bills' %}active{% endif %}">
                        <i class="fas fa-file-invoice"></i> 登录日志查询
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="content">
            <div class="header">
                <h2 class="header-title">{% block page_title %}{% endblock %}</h2>
                <div class="header-user">
                    <span>欢迎使用</span>
                    <a href="{{ url_for('logout') }}" class="btn btn-danger btn-sm">退出账号</a>
                </div>
            </div>
            
            <div class="main-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
