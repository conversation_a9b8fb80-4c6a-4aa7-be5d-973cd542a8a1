{% extends "base.html" %}

{% block page_title %}车辆进出管理{% endblock %}

{% block content %}
<div class="table-container">
    <div class="table-header">
        <div class="search-form">
            <input type="text" placeholder="请输入车牌号" class="search-input" id="carNumberInput">
            <input type="text" placeholder="请输入车位号" class="search-input" id="spaceNumberInput">
            <button class="btn btn-info btn-sm" onclick="searchRecords()">搜索</button>
        </div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>序号</th>
                <th>车牌号</th>
                <th>驶入时间</th>
                <th>驶出时间</th>
                <th>车位区域</th>
                <th>车位号</th>
                <th>收费</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="recordTableBody">
            <!-- 数据将通过JavaScript动态加载 -->
        </tbody>
    </table>
    
    <div class="pagination" id="pagination">
        <!-- 分页将通过JavaScript动态生成 -->
    </div>
</div>

<!-- 车辆驶入模态框 -->
<div id="entryModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 400px;">
        <h3 style="margin-bottom: 20px;">车辆驶入</h3>
        <form id="entryForm">
            <div class="form-group">
                <label>车牌号</label>
                <input type="text" name="car_number" class="form-control" required>
            </div>
            <div class="form-group">
                <label>车位号</label>
                <select name="space_number" class="form-control" id="availableSpaces" required>
                    <option value="">请选择车位</option>
                </select>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideEntryModal()">取消</button>
                <button type="submit" class="btn btn-primary">确认驶入</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;

// 页面加载时获取泊车记录列表
$(document).ready(function() {
    loadRecords();
    loadAvailableSpaces();
});

// 加载泊车记录列表
function loadRecords(page = 1) {
    currentPage = page;
    $.get('/api/parking_records?page=' + page, function(data) {
        displayRecords(data.records);
        displayPagination(data.current_page, data.pages, data.total);
    });
}

// 加载可用车位
function loadAvailableSpaces() {
    $.get('/api/parking_spaces', function(data) {
        const select = $('#availableSpaces');
        select.empty();
        select.append('<option value="">请选择车位</option>');
        
        data.spaces.forEach(space => {
            if (space.status === '空闲中') {
                select.append(`<option value="${space.space_number}">${space.space_number} - ${space.area} (${space.hourly_rate}元/时)</option>`);
            }
        });
    });
}

// 显示泊车记录列表
function displayRecords(records) {
    const tbody = $('#recordTableBody');
    tbody.empty();
    
    records.forEach((record, index) => {
        const exitTime = record.exit_time || '未驶出';
        const exitButton = record.exit_time ? 
            '<button class="btn btn-secondary btn-sm" disabled>已驶出</button>' :
            `<button class="btn btn-warning btn-sm" onclick="exitParking(${record.id})">驶出打票</button>`;
        
        const row = `
            <tr>
                <td>${(currentPage - 1) * 10 + index + 1}</td>
                <td>${record.car_number}</td>
                <td>${record.entry_time}</td>
                <td>${exitTime}</td>
                <td>${record.area}</td>
                <td>${record.space_number}</td>
                <td>${record.hourly_rate}元/时</td>
                <td>${exitButton}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 显示分页
function displayPagination(currentPage, totalPages, totalItems) {
    const pagination = $('#pagination');
    pagination.html(`
        <span>共 ${totalItems} 条，共 ${totalPages} 页</span>
        <button class="btn btn-sm" onclick="loadRecords(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>
        <span>第 ${currentPage} 页</span>
        <button class="btn btn-sm" onclick="loadRecords(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>
    `);
}

// 显示车辆驶入模态框
function showEntryModal() {
    loadAvailableSpaces();
    $('#entryModal').show();
}

// 隐藏车辆驶入模态框
function hideEntryModal() {
    $('#entryModal').hide();
    const form = $('#entryForm')[0];
    form.reset();
    // 重置按钮状态
    $(form).find('button[type="submit"]').prop('disabled', false).text('确认驶入');
}

// 车辆驶入表单提交
$('#entryForm').submit(function(e) {
    e.preventDefault();

    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('提交中...');

    const formData = {
        car_number: $('input[name="car_number"]').val(),
        space_number: $('select[name="space_number"]').val()
    };

    $.ajax({
        url: '/api/parking_records',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('车辆驶入成功');
            hideEntryModal();
            loadRecords(currentPage);
        },
        error: function(xhr) {
            const error = JSON.parse(xhr.responseText);
            alert('驶入失败：' + error.error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

// 车辆驶出
function exitParking(recordId) {
    if (confirm('确定要为该车辆办理驶出手续吗？')) {
        $.ajax({
            url: '/api/parking_records/' + recordId + '/exit',
            method: 'PUT',
            success: function(response) {
                alert(`车辆驶出成功！\n停车时长：${response.parking_hours}小时\n停车费用：${response.total_fee}元`);
                loadRecords(currentPage);
            },
            error: function(xhr) {
                const error = JSON.parse(xhr.responseText);
                alert('驶出失败：' + error.error);
            }
        });
    }
}

// 搜索记录
function searchRecords() {
    const carNumber = $('#carNumberInput').val().trim();
    const spaceNumber = $('#spaceNumberInput').val().trim();

    let searchParams = [];
    if (carNumber) searchParams.push('car_number=' + encodeURIComponent(carNumber));
    if (spaceNumber) searchParams.push('space_number=' + encodeURIComponent(spaceNumber));

    if (searchParams.length > 0) {
        $.get('/api/parking_records?' + searchParams.join('&'), function(data) {
            displayRecords(data.records);
            displayPagination(1, 1, data.records.length);
        }).fail(function() {
            alert('搜索失败，请重试');
        });
    } else {
        loadRecords(1);
    }
}

// 在页面顶部添加驶入按钮
$(document).ready(function() {
    $('.search-form').append('<button class="btn btn-success btn-sm" onclick="showEntryModal()" style="margin-left: 10px;">车辆驶入</button>');
});
</script>
{% endblock %}
