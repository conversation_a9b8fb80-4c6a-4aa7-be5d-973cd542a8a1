# 停车场管理系统部署指南

## 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.7 或更高版本
- MySQL 5.7 或更高版本
- 至少 1GB 可用内存
- 至少 500MB 可用磁盘空间

#### 检查Python版本
```bash
python --version
```

#### 检查MySQL服务
```bash
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
```

### 2. 数据库配置

#### 创建数据库
登录MySQL并执行：
```sql
CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 创建数据库用户（可选）
```sql
CREATE USER 'parking_user'@'localhost' IDENTIFIED BY 'parking_password';
GRANT ALL PRIVILEGES ON parking_system.* TO 'parking_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 项目部署

#### 下载项目
```bash
# 如果使用Git
git clone <repository_url>
cd parking

# 或者直接解压项目文件到目录
```

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 配置环境变量
编辑 `.env` 文件：
```env
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=parking_system
SECRET_KEY=your_secret_key_here
```

#### 初始化数据库
```bash
python init_db.py
```

#### 启动系统
```bash
python start.py
```

### 4. 访问系统

打开浏览器访问：http://localhost:5000

默认账号：
- 管理员：admin / admin123
- 普通用户：user / user123

## 生产环境部署

### 1. 使用Gunicorn部署

#### 安装Gunicorn
```bash
pip install gunicorn
```

#### 启动应用
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 2. 使用Nginx反向代理

#### 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置Nginx
创建配置文件 `/etc/nginx/sites-available/parking`:
```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/project/static;
        expires 30d;
    }
}
```

#### 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/parking /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. 使用Systemd管理服务

创建服务文件 `/etc/systemd/system/parking.service`:
```ini
[Unit]
Description=Parking Management System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable parking
sudo systemctl start parking
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 检查数据库配置信息是否正确
- 检查防火墙设置

### 2. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :5000

# 杀死占用进程
kill -9 <PID>
```

### 3. 权限问题
```bash
# 给予执行权限
chmod +x start.py

# 检查文件所有者
chown -R www-data:www-data /path/to/project
```

### 4. 静态文件不显示
- 检查static目录权限
- 检查Nginx配置
- 清除浏览器缓存

## 性能优化

### 1. 数据库优化
- 为常用查询字段添加索引
- 定期清理过期数据
- 使用数据库连接池

### 2. 应用优化
- 使用Redis缓存
- 启用Gzip压缩
- 优化静态资源

### 3. 服务器优化
- 增加worker进程数
- 调整内存配置
- 使用CDN加速

## 备份策略

### 1. 数据库备份
```bash
# 每日备份
mysqldump -u root -p parking_system > backup_$(date +%Y%m%d).sql

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backup/parking"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p parking_system > $BACKUP_DIR/parking_$DATE.sql
find $BACKUP_DIR -name "parking_*.sql" -mtime +7 -delete
```

### 2. 应用备份
```bash
# 备份整个应用目录
tar -czf parking_backup_$(date +%Y%m%d).tar.gz /path/to/parking
```

## 监控和日志

### 1. 应用日志
在app.py中添加日志配置：
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/parking.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### 2. 系统监控
使用工具如Prometheus、Grafana等监控系统性能。

## 安全建议

1. 修改默认密码
2. 使用HTTPS
3. 定期更新依赖
4. 限制数据库访问权限
5. 启用防火墙
6. 定期备份数据

## 技术支持

如遇到问题，请检查：
1. 系统日志
2. 数据库连接
3. 网络配置
4. 权限设置

联系方式：[您的联系信息]
