# 快速启动指南

## 方法一：自动配置（推荐）

### 1. 运行配置向导
```bash
python config_wizard.py
```
按提示输入MySQL配置信息

### 2. 初始化数据库
```bash
python init_db.py
```

### 3. 启动系统
```bash
python run.py
```

### 4. 访问系统
打开浏览器访问: http://localhost:5000
- 管理员账号: admin / admin123
- 普通用户账号: user / user123

---

## 方法二：手动配置

### 1. 确保MySQL服务运行
```bash
# Windows
net start mysql

# 或者通过服务管理器启动MySQL服务
```

### 2. 创建数据库
登录MySQL并执行：
```sql
CREATE DATABASE parking_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 修改配置文件
编辑 `.env` 文件，设置正确的数据库密码：
```
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=你的MySQL密码
MYSQL_DATABASE=parking_system
SECRET_KEY=your-secret-key-here
```

### 4. 初始化数据库
```bash
python init_db.py
```

### 5. 启动系统
```bash
python run.py
```

---

## 常见问题

### Q: 提示"Access denied for user 'root'@'localhost'"
A: MySQL密码不正确，请：
1. 确认MySQL root用户密码
2. 运行配置向导重新设置: `python config_wizard.py`

### Q: 提示"Can't connect to MySQL server"
A: MySQL服务未启动，请：
1. Windows: 运行 `net start mysql`
2. 或通过服务管理器启动MySQL服务

### Q: 端口5000被占用
A: 修改run.py中的端口号，或者杀死占用进程

### Q: 页面显示不正常
A: 清除浏览器缓存，或使用无痕模式访问

---

## 测试系统

启动系统后，可以运行测试脚本：
```bash
python test_system.py
```

---

## 系统功能

1. **用户管理**: 添加、编辑、删除用户信息
2. **车位管理**: 管理停车位信息和收费标准
3. **进出管理**: 车辆驶入驶出登记
4. **账单查询**: 查看停车费用和统计信息

系统会自动计算停车费用（不满一小时按一小时计算）。
